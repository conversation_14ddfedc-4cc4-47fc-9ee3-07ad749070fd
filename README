Tioga is a library for overset grid assembly on parallel distributed systems
Copyright (C) 2015 TIOGA developers

This library is free software; you can redistribute it and/or
modify it under the terms of the BSD 3-Clause License. 

This library is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
ME<PERSON><PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

You should have received a copy of the BSD 3-Clause License along 
with this library; if not, write to the Free Software
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA

TIOGA - Topology Independent Overset Grid Assembler

Functionality:
--------------

TIOGA can perform overset grid connectivity in 3-D between
multiple unstructured (or structured) meshes that are in a distributed
computing environment, i.e. each mesh is partitioned in to multiple
parts. It can accept high-order call-back functions to perform p-consistent
interpolation and searches for formulations with internal degrees of freedom
within a computational element.

News:
-----
TIOGA is currently under development with funding from the DoE ExaWind program
towards developing overset capability in the DoE NALU code for large scale
wind farm simulations

See tioga/LICENSE

References:
-----------

<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, An overset mesh approach for 3D mixed element 
high-order discretizations, In Journal of Computational Physics, Volume 322, 2016, 
Pages 33-51, ISSN 0021-9991, https://doi.org/10.1016/j.jcp.2016.06.031.
(http://www.sciencedirect.com/science/article/pii/S002199911630256X)


Roget, B. and Sitaraman, J., "Robust and efficient overset grid assembly for partitioned unstructured meshes", 
Journal of Computational Physics, v 260, March 2014, Pages 1-24

Brazell, M., Sitaraman J. and Mavriplis D.,"An Overset Mesh Approach for 3D Mixed
Element High Order Discretizations", Proceedings of 2014 Overset Grid Symposium,
Atlanta, GA, Oct 6-9, 2014.
http://2014.oversetgridsymposium.org/assets/presentations/3_1/Brazell_ogs_2014.pdf

Sharma, A., Ananthan, S., Sitaraman, J., Thomas, S. and Sprague, M.A., 2021. 
Overset meshes for incompressible flows: On preserving accuracy of underlying discretizations. 
Journal of Computational Physics, 428, p.109987.

