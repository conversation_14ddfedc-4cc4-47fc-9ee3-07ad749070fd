#
# This file is part of the Tioga software library
#
# Tioga  is a tool for overset grid assembly on parallel distributed systems
# Copyright (C) 2015 <PERSON>
#
# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
#

@PACKAGE_INIT@

# Compilers used by TIOGA build
set(TIOGA_CXX_COMPILER "@CMAKE_CXX_COMPILER@")
set(TIOGA_C_COMPILER "@CMAKE_C_COMPILER@")
set(TIOGA_Fortran_COMPILER "@CMAKE_Fortran_COMPILER@")

# Compiler flags used by TIOGA build
set(TIOGA_CXX_COMPILER_FLAGS "@CMAKE_CXX_FLAGS@")
set(TIOGA_C_COMPILER_FLAGS "@CMAKE_C_FLAGS@")
set(TIOGA_Fortran_COMPILER_FLAGS "@CMAKE_Fortran_FLAGS@")

set_and_check(TIOGA_INCLUDE_DIRS "@PACKAGE_INCLUDE_INSTALL_DIR@")
set_and_check(TIOGA_LIBRARY_DIRS "@PACKAGE_LIB_INSTALL_DIR@")

# Flag indicating whether TIOGA uses NODE GID
set(TIOGA_HAS_NODEGID @TIOGA_HAS_NODEGID@)

set(TIOGA_LIBRARIES "tioga")

include("${CMAKE_CURRENT_LIST_DIR}/TIOGALibraries.cmake")

if (NOT TIOGA_FIND_COMPONENTS)
    set(TIOGA_FIND_COMPONENTS "tioga")
endif ()

set(TIOGA_FOUND TRUE)
set(TIOGA_tioga_FOUND TRUE)

check_required_components(TIOGA)
